import { useState, useEffect } from 'react'
import { getStoredAuth } from '../utils/auth'

const EmployeeTasks = ({ user }) => {
  const [tasks, setTasks] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState('')
  const [activeTab, setActiveTab] = useState('available')
  const [selectedTask, setSelectedTask] = useState(null)
  const [showTaskDetail, setShowTaskDetail] = useState(false)

  useEffect(() => {
    fetchTasks()
  }, [])

  const fetchTasks = async () => {
    try {
      const auth = getStoredAuth()
      if (!auth) return

      // Get today's date for filtering
      const today = new Date().toISOString().split('T')[0]

      const response = await fetch(`/api/tasks?start_date=${today}&end_date=${today}`, {
        headers: {
          'Authorization': `Bearer ${auth.token}`
        }
      })

      if (response.ok) {
        const data = await response.json()
        setTasks(data)
      } else {
        setError('Failed to fetch tasks')
      }
    } catch (err) {
      setError('Error fetching tasks')
    } finally {
      setLoading(false)
    }
  }

  const getAvailableTasks = () => {
    // Show unassigned tasks (pending status with no assigned employee)
    return tasks.filter(task =>
      task.status === 'pending' && task.assigned_employee_id === null
    )
  }

  const getMyTasks = () => {
    // Show tasks assigned to current user
    return tasks.filter(task => task.assigned_employee_id === user.id)
  }

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending': return '#ffc107'
      case 'assigned': return '#17a2b8'
      case 'in_progress': return '#007bff'
      case 'completed': return '#28a745'
      case 'cancelled': return '#dc3545'
      default: return '#6c757d'
    }
  }

  const formatDate = (dateString) => {
    if (!dateString) return 'Not set'
    return new Date(dateString).toLocaleDateString('en-GB')
  }

  const formatCurrency = (amount) => {
    if (!amount) return '£0.00'
    return `£${parseFloat(amount).toFixed(2)}`
  }

  const calculateDistance = (postcode) => {
    // In a real app, this would use geolocation API to calculate actual distance
    // For now, return a placeholder distance based on postcode hash
    if (!postcode) return 'N/A'

    // Simple hash function to generate consistent distance for same postcode
    let hash = 0
    for (let i = 0; i < postcode.length; i++) {
      const char = postcode.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash // Convert to 32-bit integer
    }

    // Generate distance between 1-25 miles based on hash
    const distance = Math.abs(hash % 25) + 1
    return distance.toFixed(1)
  }

  const handleAcceptTask = async (taskId) => {
    try {
      const auth = getStoredAuth()
      if (!auth) return

      const response = await fetch(`/api/tasks/${taskId}/accept`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${auth.token}`
        }
      })

      if (response.ok) {
        fetchTasks()
      } else {
        const errorData = await response.json()
        setError(errorData.error || 'Failed to accept task')
      }
    } catch (err) {
      setError('Error accepting task')
    }
  }

  const handleStartTask = async (taskId) => {
    try {
      const auth = getStoredAuth()
      if (!auth) return

      const response = await fetch(`/api/tasks/${taskId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${auth.token}`
        },
        body: JSON.stringify({
          status: 'in_progress'
        })
      })

      if (response.ok) {
        fetchTasks()
      } else {
        setError('Failed to start task')
      }
    } catch (err) {
      setError('Error starting task')
    }
  }

  const handleCompleteTask = async (taskId) => {
    try {
      const auth = getStoredAuth()
      if (!auth) return

      const response = await fetch(`/api/tasks/${taskId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${auth.token}`
        },
        body: JSON.stringify({
          status: 'completed',
          actual_end_date: new Date().toISOString().split('T')[0]
        })
      })

      if (response.ok) {
        fetchTasks()
      } else {
        setError('Failed to complete task')
      }
    } catch (err) {
      setError('Error completing task')
    }
  }

  const handleTaskClick = (task) => {
    setSelectedTask(task)
    setShowTaskDetail(true)
  }

  if (loading) {
    return <div className="loading">Loading tasks...</div>
  }

  const availableTasks = getAvailableTasks()
  const myTasks = getMyTasks()

  return (
    <div className="employee-tasks">
      {error && <div className="error-message">{error}</div>}
      
      {/* Task Navigation */}
      <div className="task-nav">
        <button
          className={`nav-button ${activeTab === 'available' ? 'active' : ''}`}
          onClick={() => setActiveTab('available')}
        >
          Today's Available Tasks ({availableTasks.length})
        </button>
        <button
          className={`nav-button ${activeTab === 'my-tasks' ? 'active' : ''}`}
          onClick={() => setActiveTab('my-tasks')}
        >
          My Tasks Today ({myTasks.length})
        </button>
      </div>

      {/* Available Tasks Tab */}
      {activeTab === 'available' && (
        <div className="tasks-section">
          <h3>Today's Available Tasks</h3>
          {availableTasks.length === 0 ? (
            <div className="no-tasks">
              <p>No available tasks for today. Check back later!</p>
            </div>
          ) : (
            <div className="tasks-container">
              {availableTasks.map(task => (
                <div
                  key={task.id}
                  className={`task-card ${task.status}`}
                >
                  <div className="task-header-info">
                    <h4>{task.service_type} - {task.customer_name}</h4>
                    <div className="task-meta">
                      <span
                        className="task-status"
                        style={{ backgroundColor: getStatusColor(task.status) }}
                      >
                        {task.status.replace('_', ' ').toUpperCase()}
                      </span>

                    </div>
                  </div>

                  <div className="task-content">
                    <p><strong>Service:</strong> {task.service_type}</p>
                    <p><strong>Location:</strong> {task.customer_postcode}</p>
                    <p><strong>Date:</strong> {formatDate(task.start_date)}</p>
                  </div>

                  <div className="task-actions">
                    {task.status === 'pending' && task.assigned_employee_id === null && (
                      <button
                        className="btn-assign"
                        onClick={() => handleAcceptTask(task.id)}
                      >
                        Accept Task
                      </button>
                    )}
                    <button
                      className="btn-view"
                      onClick={() => handleTaskClick(task)}
                    >
                      View Details
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* My Tasks Tab */}
      {activeTab === 'my-tasks' && (
        <div className="tasks-section">
          <h3>My Tasks Today</h3>
          {myTasks.length === 0 ? (
            <div className="no-tasks">
              <p>You have no assigned tasks for today.</p>
            </div>
          ) : (
            <div className="tasks-container">
              {myTasks.map(task => (
                <div
                  key={task.id}
                  className={`task-card ${task.status}`}
                >
                  <div className="task-header-info">
                    <h4>{task.service_type} - {task.customer_name}</h4>
                    <span
                      className="task-status"
                      style={{ backgroundColor: getStatusColor(task.status) }}
                    >
                      {task.status.replace('_', ' ').toUpperCase()}
                    </span>
                  </div>

                  <div className="task-content">
                    <p><strong>Service:</strong> {task.service_type}</p>
                    <p><strong>Customer:</strong> {task.customer_name}</p>
                    <p><strong>Location:</strong> {task.customer_postcode}</p>
                    <p><strong>Date:</strong> {formatDate(task.start_date)} - {formatDate(task.estimated_end_date)}</p>
                    {task.actual_end_date && (
                      <p><strong>Completed:</strong> {formatDate(task.actual_end_date)}</p>
                    )}
                  </div>

                  <div className="task-actions">
                    {task.status === 'assigned' && (
                      <button
                        className="btn-view"
                        onClick={() => handleStartTask(task.id)}
                      >
                        Start Work
                      </button>
                    )}
                    {task.status === 'in_progress' && (
                      <button
                        className="btn-assign"
                        onClick={() => handleCompleteTask(task.id)}
                      >
                        Complete Task
                      </button>
                    )}
                    <button
                      className="btn-view"
                      onClick={() => handleTaskClick(task)}
                    >
                      View Details
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Task Detail Modal */}
      {showTaskDetail && selectedTask && (
        <TaskDetailModal 
          task={selectedTask}
          user={user}
          onClose={() => setShowTaskDetail(false)}
          onUpdate={fetchTasks}
        />
      )}
    </div>
  )
}

// Employee Task Detail Modal Component - Limited permissions
const TaskDetailModal = ({ task, user, onClose, onUpdate }) => {
  const [workSessions, setWorkSessions] = useState([])
  const [currentTask, setCurrentTask] = useState(task)
  const [editingWorkContent, setEditingWorkContent] = useState(null)

  useEffect(() => {
    setCurrentTask(task)
    if (task.assigned_employee_id === user.id && task.status !== 'pending') {
      fetchWorkSessions()
    }
  }, [task, user.id])

  const fetchWorkSessions = async () => {
    try {
      const auth = getStoredAuth()
      if (!auth) return

      const response = await fetch(`/api/tasks/${task.id}/sessions`, {
        headers: {
          'Authorization': `Bearer ${auth.token}`
        }
      })

      if (response.ok) {
        const sessions = await response.json()
        setWorkSessions(sessions)
      }
    } catch (err) {
      console.error('Error fetching work sessions:', err)
    }
  }

  const getStatusIcon = (status) => {
    switch (status) {
      case 'pending': return '🔴'
      case 'assigned': return '🟡'
      case 'in_progress': return '🟡'
      case 'completed': return '🟢'
      case 'cancelled': return '⚪'
      case 'skipped': return '⚪'
      default: return '⚪'
    }
  }

  const formatDate = (dateString) => {
    if (!dateString) return 'Not set'
    return new Date(dateString).toLocaleDateString('en-GB')
  }

  const calculateDistance = (postcode) => {
    if (!postcode) return 'N/A'

    let hash = 0
    for (let i = 0; i < postcode.length; i++) {
      const char = postcode.charCodeAt(i)
      hash = ((hash << 5) - hash) + char
      hash = hash & hash
    }

    const distance = Math.abs(hash % 25) + 1
    return distance.toFixed(1)
  }

  // Generate work sessions based on start and end dates
  const generateWorkSessions = () => {
    // Use actual work sessions from API if available
    if (workSessions && workSessions.length > 0) {
      return workSessions.map(session => ({
        ...session,
        start_photos: session.start_photos || [],
        end_photos: session.end_photos || [],
        total_hours: session.total_hours || 0,
        notes: session.notes || '',
        status: session.status || 'pending'
      }))
    }

    // Generate basic session structure for multi-day tasks
    const sessions = []
    const startDate = new Date(currentTask.start_date)
    const endDate = new Date(currentTask.estimated_end_date)

    for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
      const dateStr = d.toISOString().split('T')[0]
      const today = new Date().toISOString().split('T')[0]

      const session = {
        id: sessions.length + 1,
        work_date: dateStr,
        start_photos: [],
        end_photos: [],
        start_time: null,
        end_time: null,
        total_hours: 0,
        notes: '',
        status: dateStr < today ? 'completed' : (dateStr === today ? 'in_progress' : 'pending')
      }

      sessions.push(session)
    }

    return sessions
  }

  const workSessionsData = generateWorkSessions()

  const getTotalWorkHours = () => {
    return workSessionsData.reduce((total, session) => total + session.total_hours, 0)
  }

  const calculateWorkHours = (startPhotos, endPhotos) => {
    if (!startPhotos.length || !endPhotos.length) return 0

    const startTime = new Date(startPhotos[0].uploaded_at)
    const endTime = new Date(endPhotos[endPhotos.length - 1].uploaded_at)

    return Math.round((endTime - startTime) / (1000 * 60 * 60) * 100) / 100
  }

  const handlePhotoUpload = async (sessionId, photoType, files) => {
    console.log(`Uploading ${photoType} photos for session ${sessionId}:`, files)
    // In real app, would upload to server and update session data
    // For now, simulate photo upload with timestamp
    const uploadedPhotos = Array.from(files).map((file, index) => ({
      id: Date.now() + index,
      file_name: file.name,
      timestamp: new Date().toLocaleTimeString('en-GB'),
      uploaded_at: new Date().toISOString(),
      file: file
    }))

    // Update the session with new photos
    // This would normally be done via API call
    alert(`${photoType} photos uploaded successfully!`)
  }

  const handleWorkSummaryChange = async (sessionId, workSummary) => {
    try {
      const auth = getStoredAuth()
      if (!auth) return

      const response = await fetch(`/api/sessions/${sessionId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${auth.token}`
        },
        body: JSON.stringify({
          notes: workSummary
        })
      })

      if (response.ok) {
        // Update local state
        setWorkSessions(prev => prev.map(session =>
          session.id === sessionId
            ? { ...session, notes: workSummary }
            : session
        ))
      } else {
        console.error('Failed to update work summary')
      }
    } catch (err) {
      console.error('Error updating work summary:', err)
    }
  }

  return (
    <div className="modal-overlay" onClick={onClose}>
      <div className="modal-content task-detail-modal-large" onClick={(e) => e.stopPropagation()}>
        {/* Close button in top-right corner */}
        <button className="close-button-fixed" onClick={onClose}>×</button>

        <div className="modal-header-simple">
          <h3>{getStatusIcon(task.status)} {task.service_type} - {task.customer_name}</h3>
        </div>

        <div className="modal-body">
          <div className="task-detail-grid">
            <div className="detail-section">
              <h4>👤 Customer Information</h4>
              <p><strong>Name:</strong> {task.customer_name}</p>
              <p><strong>Phone:</strong> {task.customer_phone}</p>
              <p><strong>Postcode:</strong> {task.customer_postcode}</p>
              {task.customer_email && <p><strong>Email:</strong> {task.customer_email}</p>}

            </div>

            <div className="detail-section">
              <h4>🔧 Service Details</h4>
              <p><strong>Type:</strong> {task.service_type}</p>
              <p><strong>Description:</strong> {task.service_description}</p>
              <p><strong>Status:</strong> {task.status.replace('_', ' ').toUpperCase()}</p>
            </div>

            <div className="detail-section">
              <h4>📅 Schedule & Assignment</h4>
              <p><strong>Start Date:</strong> {formatDate(task.start_date)}</p>
              <p><strong>Est. End Date:</strong> {formatDate(task.estimated_end_date)}</p>
              {task.actual_end_date && (
                <p><strong>Actual End:</strong> {formatDate(task.actual_end_date)}</p>
              )}
              <p><strong>Assigned Employee:</strong> {task.assigned_employee_name || 'Unassigned'}</p>
            </div>
          </div>

          {/* Work Timeline */}
          {(currentTask.assigned_employee_id === user.id || currentTask.status === 'pending') && (
            <div className="timeline-section">
              <h4>📊 Work Timeline & Photo Documentation</h4>

              {currentTask.status === 'pending' ? (
                <div className="pending-info">
                  <p>This task is available for acceptance. Click "Accept Task" to take on this work.</p>
                  <div className="task-requirements">
                    <h5>Task Requirements:</h5>
                    <ul>
                      <li>Photo verification required (start and end photos)</li>
                      <li>GPS location tracking for work verification</li>
                      <li>Complete work within scheduled timeframe</li>
                      <li>Update task status upon completion</li>
                    </ul>
                  </div>
                </div>
              ) : (
                <div className="work-timeline">
                  <div className="timeline-summary">
                    <p><strong>Total Work Hours:</strong> {getTotalWorkHours()} hours (Actual)</p>
                  </div>

                  {workSessionsData.map((session, index) => {
                    const isToday = session.work_date === new Date().toISOString().split('T')[0]
                    const canEdit = currentTask.status === 'in_progress' && isToday

                    return (
                      <div key={session.id} className={`timeline-day ${session.status} ${canEdit ? 'today-editable' : ''}`}>
                        <div className="day-header">
                          <h5>
                            {getStatusIcon(session.status)} Day {index + 1} - {new Date(session.work_date).toLocaleDateString('en-GB')}
                            {isToday && <span className="today-badge">Today</span>}
                          </h5>
                          <div className="day-summary">
                            <span className="work-hours">{session.total_hours || 0}h</span>
                            {session.start_time && session.end_time && (
                              <span className="work-time">{session.start_time} - {session.end_time}</span>
                            )}
                          </div>
                        </div>

                        {/* Work Content Input */}
                        <div className="work-content-section">
                          <div className="work-content-header">
                            <h6>📝 Work Content</h6>
                            {canEdit && (
                              <button
                                className="btn-edit-small"
                                onClick={() => setEditingWorkContent(editingWorkContent === session.id ? null : session.id)}
                              >
                                {editingWorkContent === session.id ? 'Save' : 'Edit'}
                              </button>
                            )}
                          </div>

                          {canEdit && editingWorkContent === session.id ? (
                            <div className="work-content-input">
                              <textarea
                                placeholder="Describe the work completed today..."
                                value={session.notes || ''}
                                onChange={(e) => handleWorkSummaryChange(session.id, e.target.value)}
                                rows="4"
                                className="work-summary-textarea"
                                autoFocus
                              />
                              <div className="work-content-info">
                                <small>💡 Tip: Include details about tasks completed, materials used, and any issues encountered.</small>
                              </div>
                            </div>
                          ) : (
                            <div className="work-content-display">
                              {session.notes ? (
                                <p className="work-content-text">{session.notes}</p>
                              ) : (
                                <p className="work-content-placeholder">
                                  {canEdit ? 'Click "Edit" to add work content for today.' : 'No work content recorded yet.'}
                                </p>
                              )}
                            </div>
                          )}
                        </div>

                        {/* Photo Documentation */}
                        <div className="photo-documentation">
                          <div className="photo-section">
                            <h6>📷 Start Photos</h6>
                            <div className="photo-grid">
                              {session.start_photos.map((photo, photoIndex) => (
                                <div key={photoIndex} className="photo-item">
                                  <div className="photo-placeholder">
                                    📷 START {photoIndex + 1}
                                  </div>
                                  <div className="photo-info">
                                    <span className="photo-time">{photo.timestamp}</span>
                                  </div>
                                </div>
                              ))}
                              {canEdit && session.start_photos.length === 0 && (
                                <div className="photo-upload-area">
                                  <label>Upload Start Photos</label>
                                  <input
                                    type="file"
                                    multiple
                                    accept="image/*"
                                    onChange={(e) => handlePhotoUpload(session.id, 'start', e.target.files)}
                                  />
                                  <small className="upload-hint">📱 Take photos when starting work today</small>
                                </div>
                              )}
                              {!canEdit && session.start_photos.length === 0 && (
                                <div className="photo-placeholder-empty">
                                  <p>No start photos uploaded</p>
                                </div>
                              )}
                            </div>
                          </div>

                          <div className="photo-section">
                            <h6>📷 End Photos</h6>
                            <div className="photo-grid">
                              {session.end_photos.map((photo, photoIndex) => (
                                <div key={photoIndex} className="photo-item">
                                  <div className="photo-placeholder">
                                    📷 END {photoIndex + 1}
                                  </div>
                                  <div className="photo-info">
                                    <span className="photo-time">{photo.timestamp}</span>
                                  </div>
                                </div>
                              ))}
                              {canEdit && session.end_photos.length === 0 && (
                                <div className="photo-upload-area">
                                  <label>Upload End Photos</label>
                                  <input
                                    type="file"
                                    multiple
                                    accept="image/*"
                                    onChange={(e) => handlePhotoUpload(session.id, 'end', e.target.files)}
                                  />
                                  <small className="upload-hint">📱 Take photos when completing work today</small>
                                </div>
                              )}
                              {!canEdit && session.end_photos.length === 0 && (
                                <div className="photo-placeholder-empty">
                                  <p>No end photos uploaded</p>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    )
                  })}}
                </div>
              )}
            </div>
          )}
        </div>

        <div className="modal-footer">
          <button className="btn-cancel" onClick={onClose}>Close</button>

          {/* Action buttons based on task status and assignment */}
          {task.status === 'pending' && task.assigned_employee_id === null && (
            <button
              className="btn-assign"
              onClick={async () => {
                try {
                  const auth = getStoredAuth()
                  if (!auth) return

                  const response = await fetch(`/api/tasks/${task.id}/accept`, {
                    method: 'PUT',
                    headers: {
                      'Content-Type': 'application/json',
                      'Authorization': `Bearer ${auth.token}`
                    }
                  })

                  if (response.ok) {
                    onUpdate()
                    onClose()
                  } else {
                    const errorData = await response.json()
                    alert(errorData.error || 'Failed to accept task')
                  }
                } catch (err) {
                  alert('Error accepting task')
                }
              }}
            >
              Accept This Task
            </button>
          )}

          {task.status === 'assigned' && task.assigned_employee_id === user.id && (
            <button
              className="btn-view"
              onClick={async () => {
                try {
                  const auth = getStoredAuth()
                  if (!auth) return

                  const response = await fetch(`/api/tasks/${task.id}`, {
                    method: 'PUT',
                    headers: {
                      'Content-Type': 'application/json',
                      'Authorization': `Bearer ${auth.token}`
                    },
                    body: JSON.stringify({
                      status: 'in_progress'
                    })
                  })

                  if (response.ok) {
                    onUpdate()
                    onClose()
                  } else {
                    alert('Failed to start task')
                  }
                } catch (err) {
                  alert('Error starting task')
                }
              }}
            >
              Start Work
            </button>
          )}

          {task.status === 'in_progress' && task.assigned_employee_id === user.id && (
            <button
              className="btn-assign"
              onClick={async () => {
                try {
                  const auth = getStoredAuth()
                  if (!auth) return

                  const response = await fetch(`/api/tasks/${task.id}`, {
                    method: 'PUT',
                    headers: {
                      'Content-Type': 'application/json',
                      'Authorization': `Bearer ${auth.token}`
                    },
                    body: JSON.stringify({
                      status: 'completed',
                      actual_end_date: new Date().toISOString().split('T')[0]
                    })
                  })

                  if (response.ok) {
                    onUpdate()
                    onClose()
                  } else {
                    alert('Failed to complete task')
                  }
                } catch (err) {
                  alert('Error completing task')
                }
              }}
            >
              Complete Task
            </button>
          )}
        </div>
      </div>
    </div>
  )
}

export default EmployeeTasks
