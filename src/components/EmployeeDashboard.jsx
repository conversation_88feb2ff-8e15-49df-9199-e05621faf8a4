import { useState, useEffect } from 'react'
import EmployeeTasks from './EmployeeTasks'
import { getStoredAuth } from '../utils/auth'

const EmployeeDashboard = ({ user, onLogout }) => {
  const [currentTime, setCurrentTime] = useState(new Date())
  const [activeTab, setActiveTab] = useState('overview')
  const [attendance, setAttendance] = useState(null)
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentTime(new Date())
    }, 1000)

    // Fetch attendance status on component mount
    fetchAttendance()

    return () => clearInterval(timer)
  }, [])

  const fetchAttendance = async () => {
    try {
      const auth = getStoredAuth()
      if (!auth) return

      const response = await fetch('/api/attendance', {
        headers: {
          'Authorization': `Bearer ${auth.token}`
        }
      })

      if (response.ok) {
        const data = await response.json()
        setAttendance(data)
      }
    } catch (err) {
      console.error('Error fetching attendance:', err)
    }
  }

  const handleClockIn = async () => {
    setLoading(true)
    try {
      const auth = getStoredAuth()
      if (!auth) return

      const response = await fetch('/api/attendance/clock-in', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${auth.token}`
        }
      })

      if (response.ok) {
        const data = await response.json()
        alert(data.message)
        fetchAttendance()
      } else {
        const errorData = await response.json()
        alert(errorData.error || 'Failed to clock in')
      }
    } catch (err) {
      alert('Error clocking in')
    } finally {
      setLoading(false)
    }
  }

  const handleClockOut = async () => {
    setLoading(true)
    try {
      const auth = getStoredAuth()
      if (!auth) return

      const response = await fetch('/api/attendance/clock-out', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${auth.token}`
        }
      })

      if (response.ok) {
        const data = await response.json()
        alert(data.message)
        fetchAttendance()
      } else {
        const errorData = await response.json()
        alert(errorData.error || 'Failed to clock out')
      }
    } catch (err) {
      alert('Error clocking out')
    } finally {
      setLoading(false)
    }
  }

  const formatTime = (date) => {
    return date.toLocaleTimeString('en-GB', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    })
  }

  const formatDate = (date) => {
    return date.toLocaleDateString('en-GB', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const handleDashboardClick = () => {
    setActiveTab('overview')
  }

  const renderContent = () => {
    switch (activeTab) {
      case 'tasks':
        return <EmployeeTasks user={user} />
      default:
        return (
          <>
            <div className="dashboard-section">
              <h2>Today's Overview</h2>
              <div className="time-display">
                <div className="current-time">
                  <h3>{formatTime(currentTime)}</h3>
                  <p>{formatDate(currentTime)}</p>
                </div>
              </div>
            </div>

            <div className="dashboard-section">
              <h2>My Tasks</h2>
              <div className="tasks-container">
                <div className="task-card pending">
                  <h4>Kitchen Repair - 123 High Street</h4>
                  <p>Fix leaking tap and replace cabinet door</p>
                  <span className="task-status">Pending</span>
                </div>
                <div className="task-card in-progress">
                  <h4>Bathroom Installation - 456 Oak Avenue</h4>
                  <p>Install new shower unit and tiles</p>
                  <span className="task-status">In Progress</span>
                </div>
                <div className="task-card completed">
                  <h4>Garden Fence - 789 Elm Road</h4>
                  <p>Replace damaged fence panels</p>
                  <span className="task-status">Completed</span>
                </div>
              </div>
            </div>

            <div className="dashboard-section">
              <h2>Quick Actions</h2>
              <div className="attendance-status">
                {attendance && (
                  <div className={`status-card ${attendance.status}`}>
                    <h4>Today's Status: {attendance.status === 'clocked_in' ? '🟢 Clocked In' : '🔴 Clocked Out'}</h4>
                    {attendance.clock_in_time && (
                      <p>Clock In: {new Date(attendance.clock_in_time).toLocaleTimeString('en-GB')}</p>
                    )}
                    {attendance.clock_out_time && (
                      <p>Clock Out: {new Date(attendance.clock_out_time).toLocaleTimeString('en-GB')}</p>
                    )}
                    {attendance.total_work_hours && (
                      <p>Total Hours: {attendance.total_work_hours}h</p>
                    )}
                  </div>
                )}
              </div>
              <div className="actions-grid">
                {attendance?.status === 'clocked_out' ? (
                  <button
                    className="action-button clock-in"
                    onClick={handleClockIn}
                    disabled={loading}
                  >
                    {loading ? 'Clocking In...' : '🟢 Clock In'}
                  </button>
                ) : (
                  <button
                    className="action-button clock-out"
                    onClick={handleClockOut}
                    disabled={loading}
                  >
                    {loading ? 'Clocking Out...' : '🔴 Clock Out'}
                  </button>
                )}
                <button
                  className="action-button"
                  onClick={() => setActiveTab('tasks')}
                >
                  View Tasks
                </button>
                <button className="action-button">Report Issue</button>
                <button className="action-button">View Schedule</button>
              </div>
            </div>

            <div className="dashboard-section">
              <h2>Recent Activity</h2>
              <div className="activity-list">
                <div className="activity-item">
                  <span className="activity-time">09:30</span>
                  <span className="activity-text">Started task at 123 High Street</span>
                </div>
                <div className="activity-item">
                  <span className="activity-time">08:45</span>
                  <span className="activity-text">Clocked in for the day</span>
                </div>
                <div className="activity-item">
                  <span className="activity-time">Yesterday</span>
                  <span className="activity-text">Completed bathroom installation</span>
                </div>
              </div>
            </div>
          </>
        )
    }
  }

  return (
    <div className="dashboard">
      <header className="dashboard-header">
        <h1 className="dashboard-title" onClick={handleDashboardClick}>Dashboard</h1>
        <div className="user-info">
          <span>Welcome, {user.full_name}</span>
          <button onClick={onLogout} className="logout-button">Logout</button>
        </div>
      </header>

      <nav className="dashboard-nav">
        <button
          className={`nav-button ${activeTab === 'overview' ? 'active' : ''}`}
          onClick={() => setActiveTab('overview')}
        >
          Overview
        </button>
        <button
          className={`nav-button ${activeTab === 'tasks' ? 'active' : ''}`}
          onClick={() => setActiveTab('tasks')}
        >
          Tasks
        </button>
      </nav>

      <main className="dashboard-content">
        {renderContent()}
      </main>
    </div>
  )
}

export default EmployeeDashboard
